entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: web-secure
          scheme: https
  web-secure:
    address: ":443"
  metrics:
    # only listen on lo for metrics
    address: "127.0.0.1:8082"
metrics:
  prometheus:
    entryPoint: metrics
providers:
  file:
    directory: "%[3]s/proxy"
    watch: true
certificatesResolvers:
  resolver:
    acme:
      email: "%[2]s"
      storage: "%[1]s/traefik/acme.json"
      httpChallenge:
        # used during the challenge
        entryPoint: web
  dnsresolver:
    acme:
      email: "%[2]s"
      storage: "%[1]s/traefik/acme2.json"
      dnsChallenge:
        provider: exec
