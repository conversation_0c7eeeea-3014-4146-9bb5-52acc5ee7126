By @leesmet

# Deployments on grid 3.0

A `deployment` consists of one or more `DeploymentItems`. These
`DeploymentItems` are what used to be the `Workloads`. A single
`deployment` can consist of multiple `DeploymentItems`. All of these are
grouped together, so that signatures on the `deployment` encompass all
the included items (i.e. whoever signs necessarily knows about all
elements in the deployment).

As there is no longer a centralized `explorer` component, it is now the
responsibility of the client to ensure all nodes which have a workload
to deploy actually receive it. In practice this will be handled by the
message bus.

## Identification

Since there is no centralized `explorer`, there is no more `ID`
generation. A deployment is now identified by an `originator_twin_id`
(the ID of the twin creating the request, as registered in the grid DB),
and an `originator_deployment_id`, which is a unique ID generated by the
creator of the deployment.

## Expiration

A deployment can optionally expire, in which case the `expiration` field
will be set to a non-zero value. In this case, the value is the unix
timestamp of when the deployment expires, and the node should
automatically delete all attached deployment items. If set to 0, the
deployment does not expire automatically.

## Tracking deployment items

Every deployment item is given a unique name. The name must be unique in
the entire deployment. It is allowed to reuse names in different
deployments however.

## Deployment adaptability and lifecycle

Over time, the requirements and needs of a deployment can change. For
such a scenario, the deployment is made adaptable as follows:

- The root `Deployment` object has a `version` field.
- Every `DeploymentItem` in this `Deployment` also has a version field.

Whenever a change to a deployment item is done, or a new deployment item
is added or removed (in other words, whenever anything in the whole
deployment changes), the root version is incremented. Every deployment
item for which something needs to happen, **MUST** have its version
field set to the same value as the version field in the deployment root.
This leads to the following possibilities.

- New items can be added. This can be seen because a new name will be
	added in the deployment. The version field on these items must be
	the same as the version field on the deployment root.
- Items can be removed. In this case, the item is simply no longer
	present in the deployment, and zero-os needs to decommission it.
	This can be identified because the `name` field will not be present
	anymore.
- An item needs to be adapted. The item was already present in a
	previous version. The deployed instance of the item must be adapted
	to match the new description. Regardless of when an item was last
	updated, the version field of the item must match the version field
	of the deployment.

If an item does not need to change in a new version, the version field
on the item itself can remain at the old value. This means it is
possible to have items with a lower version number than the deployment
version.

Since it is possible to change the signature requirement of the deployment, any
new version which does so (i.e. if the signature requirement for the new version
is different to the previous one) must also make sure the signatures on the new
version would fulfill the signatures on the old version. E.g. assume there are 3
twins which can sign, 1, 2, and 3, and at least 2 signatures are needed. If a new
version then changes the signature requirements to have twin 4, 5 and 6 sign (again
2 signatures), then the signatures required in this version would be: at least 2
signatures from the group 1,2,3 and at least 2 signatures from the group 4,5,6.
In case the groups overlap (e.g. 1,2,3 and 3,4,5), the overlapping signature counts
towards both the old requirement and the new requirement (e.g. in the previous example,
a signature of 2, 3, and 4 would be sufficient).

## Payment

Payment is handled by the farmer twin for the node. Every x amount of time, a
usage report is sent by the node to the farmer twin. This usage report specifies
the amount of resources consumed by the workloads of the deployment. The farm twin
aggregates these usage reports and sends a payment request every Y amount of time
to the payment provider.

## Payment provider

Payment providers are a list of twins willing to pay for a deployment. In the
trivial case, the list will be empty, and the payment request must be directed
to the twin identified by the `originator_twin_id`. In case payment providers are
set, the payment request must be directed to the specified twin id, and the
secret must be attached

## Known issues

- payment provider secrets: we don't have asymmetric encryption keys, only a
signing key. Regardless, encryption might not be needed since a secret won't be
guessable anyway. Perhaps another signature here is better.
- signature input is not correct in the current examples, to fix (some fields are
not covered).

## Important workload changes

- A volume is **only** allowed on **SSD**
- A zero-db must have its data on **HDD**, but store the index on
	**SSD**. There can only be 1 zero-db per hard disk.
