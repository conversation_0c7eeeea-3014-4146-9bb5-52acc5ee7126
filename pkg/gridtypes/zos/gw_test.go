package zos

import (
	"testing"

	"github.com/hashicorp/go-multierror"
	"github.com/stretchr/testify/require"
)

func TestValidBackend(t *testing.T) {
	require := require.New(t)

	t.Run("test ip:port cases", func(t *testing.T) {
		backend := Backend("*******:10")
		err := backend.Valid(true)
		require.NoError(err)

		backend = Backend("*******:10")
		err = backend.Valid(false)
		require.Error(err)

		backend = Backend("*******")
		err = backend.Valid(true)
		require.Error(err)

		backend = Backend("*******:port")
		err = backend.Valid(true)
		require.Error(err)

		backend = Backend("ip:10")
		err = backend.Valid(true)
		require.Error(err)

		backend = Backend("*******:1000000")
		err = backend.Valid(true)
		require.Error(err)
	})

	t.Run("test http://ip:port cases", func(t *testing.T) {
		backend := Backend("http://*******:10")
		err := backend.Valid(false)
		require.NoError(err)

		backend = Backend("http://*******:10")
		err = backend.Valid(true)
		require.Error(err)

		backend = Backend("http://*******:port")
		err = backend.Valid(false)
		require.Error(err)

		backend = Backend("http://ip:10")
		err = backend.Valid(false)
		require.Error(err)
	})

	t.Run("test http://ip cases", func(t *testing.T) {
		backend := Backend("http://*******")
		err := backend.Valid(false)
		require.NoError(err)

		backend = Backend("http://*******")
		err = backend.Valid(true)
		require.Error(err)

		backend = Backend("http://ip")
		err = backend.Valid(false)
		require.Error(err)
	})
}

func TestAsAddress(t *testing.T) {

	cases := []struct {
		Backend Backend
		Address string
		Err     bool
	}{
		{
			Backend: Backend("http://***********"),
			Address: "***********:80",
		},
		{
			Backend: Backend("[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:200"),
			Address: "[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:200",
		},
		{
			Backend: Backend("2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF:200"),
			Err:     true,
		},
		{
			Backend: Backend("http://[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:200"),
			Address: "[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:200",
		},
		{
			Backend: Backend("http://***********:500"),
			Address: "***********:500",
		},
		{
			Backend: Backend("***********:500"),
			Address: "***********:500",
		},
	}

	for _, c := range cases {
		t.Run(string(c.Backend), func(t *testing.T) {
			addr, err := c.Backend.AsAddress()
			if c.Err {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, c.Address, addr)
			}
		})
	}
}

func TestValidBackendIP6(t *testing.T) {
	require := require.New(t)

	t.Run("test ip:port cases", func(t *testing.T) {
		backend := Backend("[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:10")
		err := backend.Valid(true)
		require.NoError(err)

		backend = Backend("[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:10")
		err = backend.Valid(false)
		require.Error(err)

		backend = Backend("[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:port")
		err = backend.Valid(true)
		require.Error(err)

		backend = Backend("[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:1000000")
		err = backend.Valid(true)
		require.Error(err)
	})

	t.Run("test http://ip:port cases", func(t *testing.T) {
		backend := Backend("http://[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:10")
		err := backend.Valid(false)
		require.NoError(err)

		backend = Backend("http://[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:10")
		err = backend.Valid(true)
		require.Error(err)

		backend = Backend("http://[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:port")
		err = backend.Valid(false)
		require.Error(err)
	})

	t.Run("test http://ip cases", func(t *testing.T) {
		backend := Backend("http://[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]")
		err := backend.Valid(false)
		require.NoError(err)

		backend = Backend("http://[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]")
		err = backend.Valid(true)
		require.Error(err)
	})
}

func TestValidateBackends(t *testing.T) {
	require := require.New(t)

	t.Run("empty backends", func(t *testing.T) {
		backends := []Backend{
			"",
		}
		err := ValidateBackends(backends, true)
		require.Error(err)

		err = ValidateBackends(backends, false)
		require.Error(err)
	})

	t.Run("all valid backends with tlsPassthrough=true", func(t *testing.T) {
		backends := []Backend{
			"*******:80",
			"*******:443",
			"[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]:8080",
		}
		err := ValidateBackends(backends, true)
		require.NoError(err)
	})

	t.Run("all valid backends with tlsPassthrough=false", func(t *testing.T) {
		backends := []Backend{
			"http://*******",
			"http://*******:443",
			"http://[2001:db8:3333:4444:CCCC:DDDD:EEEE:FFFF]",
		}
		err := ValidateBackends(backends, false)
		require.NoError(err)
	})

	t.Run("mixed valid and invalid backends with tlsPassthrough=true", func(t *testing.T) {
		backends := []Backend{
			"*******:80",
			"http://*******:443", // invalid (should be IP:port without http://)
			"*******",            // invalid (missing port)
			"*******:port",       // invalid (non-numeric port)
			"127.0.0.1:8080",
			"[::1]:8080",
			"[2001:db8::1]:8080",
			"2001:db8::1:8080", // invalid (wrong IPv6 format)
		}
		err := ValidateBackends(backends, true)
		require.Error(err)
		merr, ok := err.(*multierror.Error)
		require.True(ok)
		require.Equal(4, len(merr.Errors))
	})

	t.Run("mixed valid and invalid backends with tlsPassthrough=false", func(t *testing.T) {
		backends := []Backend{
			"http://*******",
			"*******:80", // invalid (needs http://)
			"http://*******:443",
			"https://*******",  // invalid (wrong scheme)
			"http://localhost", // invalid (loopback)
			"http://127.0.0.1", // invalid (loopback)
			"http://[::1]",     // invalid (loopback)
			"http://[2001:db8::1]:8080",
		}
		err := ValidateBackends(backends, false)
		require.Error(err)
		merr, ok := err.(*multierror.Error)
		require.True(ok)
		require.Equal(5, len(merr.Errors))
	})

	t.Run("scheme mismatch using https", func(t *testing.T) {
		backends := []Backend{
			"https://*******",
		}
		err := ValidateBackends(backends, false)
		require.Error(err)
	})

	t.Run("scheme mismatch using http with tlsPassthrough=true", func(t *testing.T) {
		backends := []Backend{
			"http://*******:80",
		}
		err := ValidateBackends(backends, true)
		require.Error(err)
	})

	t.Run("invalid backends", func(t *testing.T) {
		backends := []Backend{
			"invalid",
			"*******:port",
			"http://invalid",
			"ftp://*******",
		}

		err := ValidateBackends(backends, true)
		require.Error(err)
		merr, ok := err.(*multierror.Error)
		require.True(ok)
		require.Equal(4, len(merr.Errors))

		err = ValidateBackends(backends, false)
		require.Error(err)
		merr, ok = err.(*multierror.Error)
		require.True(ok)
		require.Equal(4, len(merr.Errors))
	})
}
