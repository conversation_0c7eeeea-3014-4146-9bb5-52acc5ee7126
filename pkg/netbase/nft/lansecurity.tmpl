flush chain inet filter forward;

table inet filter {
    chain forward {
        type filter hook forward priority filter; policy accept;

        # @th,16,16 is raw expression for sport/dport in transport header
        # used due to limitation on the installed nft v0.9.1
        meta l4proto { tcp, udp } @th,16,16 { 9650, 9651 } accept;

        # accept traffic to only default gateway
        ip daddr {{.GatewayIP}} accept;

        # drop traffic to all other ips on the subnet
        ip daddr {{.SubnetIP}} drop;
    }
}
