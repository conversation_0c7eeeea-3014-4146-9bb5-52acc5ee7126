// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/perf/iperf/exec_wrapper.go
//
// Generated by this command:
//
//	mockgen -source=pkg/perf/iperf/exec_wrapper.go -destination=pkg/perf/iperf/mock_exec_wrapper.go -package=iperf
//
// Package execwrapper is a generated GoMock package.
package execwrapper

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockExecWrapper is a mock of ExecWrapper interface.
type MockExecWrapper struct {
	ctrl     *gomock.Controller
	recorder *MockExecWrapperMockRecorder
	isgomock struct{}
}

// MockExecWrapperMockRecorder is the mock recorder for MockExecWrapper.
type MockExecWrapperMockRecorder struct {
	mock *MockExecWrapper
}

// NewMockExecWrapper creates a new mock instance.
func NewMockExecWrapper(ctrl *gomock.Controller) *MockExecWrapper {
	mock := &MockExecWrapper{ctrl: ctrl}
	mock.recorder = &MockExecWrapperMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExecWrapper) EXPECT() *MockExecWrapperMockRecorder {
	return m.recorder
}

// CommandContext mocks base method.
func (m *MockExecWrapper) CommandContext(ctx context.Context, name string, arg ...string) ExecCmd {
	m.ctrl.T.Helper()
	varargs := []any{ctx, name}
	for _, a := range arg {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommandContext", varargs...)
	ret0, _ := ret[0].(ExecCmd)
	return ret0
}

// CommandContext indicates an expected call of CommandContext.
func (mr *MockExecWrapperMockRecorder) CommandContext(ctx, name any, arg ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, name}, arg...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommandContext", reflect.TypeOf((*MockExecWrapper)(nil).CommandContext), varargs...)
}

// LookPath mocks base method.
func (m *MockExecWrapper) LookPath(file string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LookPath", file)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LookPath indicates an expected call of LookPath.
func (mr *MockExecWrapperMockRecorder) LookPath(file any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LookPath", reflect.TypeOf((*MockExecWrapper)(nil).LookPath), file)
}

// MockExecCmd is a mock of ExecCmd interface.
type MockExecCmd struct {
	ctrl     *gomock.Controller
	recorder *MockExecCmdMockRecorder
	isgomock struct{}
}

// MockExecCmdMockRecorder is the mock recorder for MockExecCmd.
type MockExecCmdMockRecorder struct {
	mock *MockExecCmd
}

// NewMockExecCmd creates a new mock instance.
func NewMockExecCmd(ctrl *gomock.Controller) *MockExecCmd {
	mock := &MockExecCmd{ctrl: ctrl}
	mock.recorder = &MockExecCmdMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExecCmd) EXPECT() *MockExecCmdMockRecorder {
	return m.recorder
}

// CombinedOutput mocks base method.
func (m *MockExecCmd) CombinedOutput() ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CombinedOutput")
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CombinedOutput indicates an expected call of CombinedOutput.
func (mr *MockExecCmdMockRecorder) CombinedOutput() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CombinedOutput", reflect.TypeOf((*MockExecCmd)(nil).CombinedOutput))
}
