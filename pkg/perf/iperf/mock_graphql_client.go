// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/perf/iperf/graphql_wrapper.go
//
// Generated by this command:
//
//	mockgen -source=pkg/perf/iperf/graphql_wrapper.go -destination=pkg/perf/iperf/mock_graphql_client.go -package=iperf
//

// Package iperf is a generated GoMock package.
package iperf

import (
	context "context"
	reflect "reflect"

	graphql "github.com/threefoldtech/zosbase/pkg/perf/graphql"
	gomock "go.uber.org/mock/gomock"
)

// MockGraphQLClient is a mock of GraphQLClient interface.
type MockGraphQLClient struct {
	ctrl     *gomock.Controller
	recorder *MockGraphQLClientMockRecorder
	isgomock struct{}
}

// MockGraphQLClientMockRecorder is the mock recorder for MockGraphQLClient.
type MockGraphQLClientMockRecorder struct {
	mock *MockGraphQLClient
}

// NewMockGraphQLClient creates a new mock instance.
func NewMockGraphQLClient(ctrl *gomock.Controller) *MockGraphQLClient {
	mock := &MockGraphQLClient{ctrl: ctrl}
	mock.recorder = &MockGraphQLClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGraphQLClient) EXPECT() *MockGraphQLClientMockRecorder {
	return m.recorder
}

// GetUpNodes mocks base method.
func (m *MockGraphQLClient) GetUpNodes(ctx context.Context, nodesNum int, farmID, excludeFarmID uint32, ipv4, ipv6 bool) ([]graphql.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUpNodes", ctx, nodesNum, farmID, excludeFarmID, ipv4, ipv6)
	ret0, _ := ret[0].([]graphql.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUpNodes indicates an expected call of GetUpNodes.
func (mr *MockGraphQLClientMockRecorder) GetUpNodes(ctx, nodesNum, farmID, excludeFarmID, ipv4, ipv6 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUpNodes", reflect.TypeOf((*MockGraphQLClient)(nil).GetUpNodes), ctx, nodesNum, farmID, excludeFarmID, ipv4, ipv6)
}
