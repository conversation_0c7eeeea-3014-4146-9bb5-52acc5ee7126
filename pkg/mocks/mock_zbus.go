// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/threefoldtech/zbus (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -destination=mock_zbus.go -package=diagnostics github.com/threefoldtech/zbus Client
//

// Package diagnostics is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	zbus "github.com/threefoldtech/zbus"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
	isgomock struct{}
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// Request mocks base method.
func (m *MockClient) Request(module string, object zbus.ObjectID, method string, args ...any) (*zbus.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{module, object, method}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Request", varargs...)
	ret0, _ := ret[0].(*zbus.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Request indicates an expected call of Request.
func (mr *MockClientMockRecorder) Request(module, object, method any, args ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{module, object, method}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Request", reflect.TypeOf((*MockClient)(nil).Request), varargs...)
}

// RequestContext mocks base method.
func (m *MockClient) RequestContext(ctx context.Context, module string, object zbus.ObjectID, method string, args ...any) (*zbus.Response, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, module, object, method}
	for _, a := range args {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RequestContext", varargs...)
	ret0, _ := ret[0].(*zbus.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestContext indicates an expected call of RequestContext.
func (mr *MockClientMockRecorder) RequestContext(ctx, module, object, method any, args ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, module, object, method}, args...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestContext", reflect.TypeOf((*MockClient)(nil).RequestContext), varargs...)
}

// Status mocks base method.
func (m *MockClient) Status(ctx context.Context, module string) (zbus.Status, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status", ctx, module)
	ret0, _ := ret[0].(zbus.Status)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Status indicates an expected call of Status.
func (mr *MockClientMockRecorder) Status(ctx, module any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockClient)(nil).Status), ctx, module)
}

// Stream mocks base method.
func (m *MockClient) Stream(ctx context.Context, module string, object zbus.ObjectID, event string) (<-chan zbus.Event, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Stream", ctx, module, object, event)
	ret0, _ := ret[0].(<-chan zbus.Event)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Stream indicates an expected call of Stream.
func (mr *MockClientMockRecorder) Stream(ctx, module, object, event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stream", reflect.TypeOf((*MockClient)(nil).Stream), ctx, module, object, event)
}
