# 1. NU reporting for private traffic

Date: 2022-10-24

## Status

Accepted

## Context

From now on, traffic to the public internet is counted, and reported as NU consumption. Not only the traffic over public ips.

## Decision

- Monitor (only bytes counters) of natted VMs traffic going to/from the public internet
- NUs are reported for network resources (network workload) not per VM.

## Consequences

All traffic generated by the VM to public internet will be billed by the user. Even if the VM has no public IP.
