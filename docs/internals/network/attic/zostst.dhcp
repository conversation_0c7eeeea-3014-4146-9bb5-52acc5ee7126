#!/usr/bin/bash

mgmtnic=(
0c:c4:7a:51:e3:6a
0c:c4:7a:51:e9:e6
0c:c4:7a:51:ea:18
0c:c4:7a:51:e3:78
0c:c4:7a:51:e7:f8
0c:c4:7a:51:e8:ba
0c:c4:7a:51:e8:0c
0c:c4:7a:51:e7:fa
)

ipminic=(
0c:c4:7a:4c:f3:b6
0c:c4:7a:4d:02:8c
0c:c4:7a:4d:02:91
0c:c4:7a:4d:02:62
0c:c4:7a:4c:f3:7e
0c:c4:7a:4d:02:98
0c:c4:7a:4d:02:19
0c:c4:7a:4c:f2:e0
)
cnt=1
for i in ${mgmtnic[*]} ; do 
cat << EOF 
config host
	option name 'zosv2tst-${cnt}'
	option dns '1'
	option mac '${i}'
	option ip '10.5.0.$((${cnt} + 10))'

EOF
let cnt++
done



cnt=1
for i in ${ipminic[*]} ; do 
cat << EOF 
config host
	option name 'ipmiv2tst-${cnt}'
	option dns '1'
	option mac '${i}'
	option ip '10.5.0.$((${cnt} + 100))'

EOF
let cnt++
done

for i in ${mgmtnic[*]} ; do 
	echo ln -s zoststconf 01-$(echo $i | sed s/:/-/g) 
done
