@startuml

() "br-pub (Public Bridge)" as brpub

note bottom of brpub
This bridge is always created on boot, and is either
connected to the zos bridge (in single nic setup).
or to the seond nic with public IPv6 (in dual nic setup)
end note


package "public namespace" {

    [public\nmacvlan] as public
    public -down- brpub: macvlan
    note right of public
    - have a static mac generated from node id
    - set the ips as configured
    - set the default gateways as configured
    end note

    note as global
    inside namespace
    - net.ipv6.conf.all.accept_ra = 2
    - net.ipv6.conf.all.accept_ra_defrtr = 1
    end note
}

@enduml
