@startuml
[zos\nbridge] as zos
[br-pub\nbridge] as brpub
[br-ndmz\nbridge] as brndmz
note top of brndmz
disable ipv6
- net.ipv6.conf.br-ndmz.disable_ipv6 = 1
end note
' brpub -left- zos : veth pair\n(tozos)
brpub -down- master
note right of master
master is found as described
in the readme (this can be zos bridge)
in case of a single node machine
end note

package "ndmz namespace" {
    [tonrs\nmacvlan] as tonrs
    note bottom of tonrs
    - net.ipv4.conf.tonrs.proxy_arp = 0
    - net.ipv6.conf.tonrs.disable_ipv6 = 0

    Addresses:
    ***********/16
    fe80::1/64
    fd00::1
    end note
    tonrs - brndmz: macvlan

    [npub6\nmacvlan] as npub6
    npub6 -down- brpub: macvlan

    [npub4\nmacvlan] as npub4
    npub4 -down- zos: macvlan

    note as MAC
    gets static mac address generated
    from node id. to make sure it receives
    same ip address.
    end note

    MAC .. npub4
    MAC .. npub6

    note as setup
    - net.ipv6.conf.all.forwarding = 1
    end note

    [ygg0]
    note bottom of ygg0
    this will be added by yggdrasil setup
    in the next step
    end note
}

footer (hidden node) no master with global unicast ipv6 found
@enduml
