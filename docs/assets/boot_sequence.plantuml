@startuml

package "node-ready"{
    [local-modprobe]
    [udev-trigger]
    [redis]
    [haveged]
    [cgroup]
    [redis]
}

package "boot" {
    [storaged]
    [internet]
    [networkd]
    [identityd]
}

package "internal modules"{
    [flistd]
    [containerd]
    [contd]
    [upgraded]
    [provisiond]
}

[local-modprobe]<-- [udev-trigger]
[udev-trigger] <-- [storaged]
[udev-trigger] <-- [internet]
[storaged] <-- [identityd]

[identityd] <- [networkd]

[internet] <-- [networkd]
[networkd] <-- [containerd]
[storaged] <-- [containerd]

[containerd] <-- [contd]

[storaged] <-- [flistd]
[networkd] <-- [flistd]

[flistd] <-- [upgraded]
[networkd] <-- [upgraded]

[networkd] <-- [provisiond]
[flistd] <-- [provisiond]
[contd] <-- [provisiond]

@enduml
