# `qsfs` type

`qsfs` short for `quantum safe file system` is a FUSE filesystem which aim to be able to support unlimited local storage with remote backend for offload and backup which cannot be broke even by a quantum computer. Please read about it [here](https://github.com/threefoldtech/quantum-storage)

To create a `qsfs` workload you need to provide the workload type as [here](../../../pkg/gridtypes/zos/qsfs.go)
