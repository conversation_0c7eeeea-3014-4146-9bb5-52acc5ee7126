name: Tests and Coverage
on: [push]

jobs:
  daemons:
    name: Running Daemon Tests
    runs-on: ubuntu-latest
    steps:
      - name: Set up Go 1.21
        uses: actions/setup-go@v1
        with:
          go-version: 1.21
        id: go

      - name: Prepare dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libjansson-dev libhiredis-dev

      - name: Checkout code into the Go module directory
        uses: actions/checkout@v1

      - name: Get dependencies
        run: |
          cd pkg
          make getdeps
        env:
          GO111MODULE: on

      - name: Run tests
        run: |
          cd pkg
          make testrace
        env:
          GO111MODULE: on
