name: Vue lint
on:
  push:
    paths:
      - client/**
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [21.x]

    steps:
      - uses: actions/checkout@v5
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'yarn'
          cache-dependency-path: ./frontend/yarn.lock
      - name: frontend
        run: cd frontend
      - name: Yarn Install
        run: cd frontend && yarn install
      - name: ESLint
        run: cd frontend && yarn lint
