name: Go testing

on:
  push:
    paths:
      - backend/**
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      redis-db:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v5
        with:
          submodules: "true"

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.21"
        id: go

      - name: Get dependencies
        run: |
          cd backend && go mod download

      - name: Test
        run: cd backend && go test -v ./app -timeout=20m -p=1 -count=1
        env: 
          TEST_MNEMONIC: ${{ secrets.TEST_MNEMONIC }}
