[package]
name = "bootstrap"
version = "0.1.0"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
edition = "2018"
build = false

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
retry = "0.5"
shlex = "0.1"
anyhow = "1.0"
reqwest = { version = "0.11", features = ["blocking", "json"]}
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
log = "0.4"
simple_logger = "1.3"
nix = "0.15"
openssl-sys = "*"
walkdir = "2"
clap = "2.33"
exec = "0.3"

[features]
# Force openssl-sys to staticly link in the openssl library. Necessary when
# cross compiling to x86_64-unknown-linux-musl.
vendored = ["openssl-sys/vendored"]
